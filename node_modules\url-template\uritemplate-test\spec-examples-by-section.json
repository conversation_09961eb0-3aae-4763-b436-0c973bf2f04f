{"3.2.1 Variable Expansion": {"variables": {"count": ["one", "two", "three"], "dom": ["example", "com"], "dub": "me/too", "hello": "Hello World!", "half": "50%", "var": "value", "who": "fred", "base": "http://example.com/home/", "path": "/foo/bar", "list": ["red", "green", "blue"], "keys": {"semi": ";", "dot": ".", "comma": ","}, "v": "6", "x": "1024", "y": "768", "empty": "", "empty_keys": [], "undef": null}, "testcases": [["{count}", "one,two,three"], ["{count*}", "one,two,three"], ["{/count}", "/one,two,three"], ["{/count*}", "/one/two/three"], ["{;count}", ";count=one,two,three"], ["{;count*}", ";count=one;count=two;count=three"], ["{?count}", "?count=one,two,three"], ["{?count*}", "?count=one&count=two&count=three"], ["{&count*}", "&count=one&count=two&count=three"]]}, "3.2.2 Simple String Expansion": {"variables": {"count": ["one", "two", "three"], "dom": ["example", "com"], "dub": "me/too", "hello": "Hello World!", "half": "50%", "var": "value", "who": "fred", "base": "http://example.com/home/", "path": "/foo/bar", "list": ["red", "green", "blue"], "keys": {"semi": ";", "dot": ".", "comma": ","}, "v": "6", "x": "1024", "y": "768", "empty": "", "empty_keys": [], "undef": null}, "testcases": [["{var}", "value"], ["{hello}", "Hello%20World%21"], ["{half}", "50%25"], ["O{empty}X", "OX"], ["O{undef}X", "OX"], ["{x,y}", "1024,768"], ["{x,hello,y}", "1024,Hello%20World%21,768"], ["?{x,empty}", "?1024,"], ["?{x,undef}", "?1024"], ["?{undef,y}", "?768"], ["{var:3}", "val"], ["{var:30}", "value"], ["{list}", "red,green,blue"], ["{list*}", "red,green,blue"], ["{keys}", ["comma,%2C,dot,.,semi,%3B", "comma,%2C,semi,%3B,dot,.", "dot,.,comma,%2C,semi,%3B", "dot,.,semi,%3B,comma,%2C", "semi,%3B,comma,%2C,dot,.", "semi,%3B,dot,.,comma,%2C"]], ["{keys*}", ["comma=%2C,dot=.,semi=%3B", "comma=%2C,semi=%3B,dot=.", "dot=.,comma=%2C,semi=%3B", "dot=.,semi=%3B,comma=%2C", "semi=%3B,comma=%2C,dot=.", "semi=%3B,dot=.,comma=%2C"]]]}, "3.2.3 Reserved Expansion": {"variables": {"count": ["one", "two", "three"], "dom": ["example", "com"], "dub": "me/too", "hello": "Hello World!", "half": "50%", "var": "value", "who": "fred", "base": "http://example.com/home/", "path": "/foo/bar", "list": ["red", "green", "blue"], "keys": {"semi": ";", "dot": ".", "comma": ","}, "v": "6", "x": "1024", "y": "768", "empty": "", "empty_keys": [], "undef": null}, "testcases": [["{+var}", "value"], ["{/var,empty}", "/value/"], ["{/var,undef}", "/value"], ["{+hello}", "Hello%20World!"], ["{+half}", "50%25"], ["{base}index", "http%3A%2F%2Fexample.com%2Fhome%2Findex"], ["{+base}index", "http://example.com/home/<USER>"], ["O{+empty}X", "OX"], ["O{+undef}X", "OX"], ["{+path}/here", "/foo/bar/here"], ["{+path:6}/here", "/foo/b/here"], ["here?ref={+path}", "here?ref=/foo/bar"], ["up{+path}{var}/here", "up/foo/barvalue/here"], ["{+x,hello,y}", "1024,Hello%20World!,768"], ["{+path,x}/here", "/foo/bar,1024/here"], ["{+list}", "red,green,blue"], ["{+list*}", "red,green,blue"], ["{+keys}", ["comma,,,dot,.,semi,;", "comma,,,semi,;,dot,.", "dot,.,comma,,,semi,;", "dot,.,semi,;,comma,,", "semi,;,comma,,,dot,.", "semi,;,dot,.,comma,,"]], ["{+keys*}", ["comma=,,dot=.,semi=;", "comma=,,semi=;,dot=.", "dot=.,comma=,,semi=;", "dot=.,semi=;,comma=,", "semi=;,comma=,,dot=.", "semi=;,dot=.,comma=,"]]]}, "3.2.4 Fragment Expansion": {"variables": {"count": ["one", "two", "three"], "dom": ["example", "com"], "dub": "me/too", "hello": "Hello World!", "half": "50%", "var": "value", "who": "fred", "base": "http://example.com/home/", "path": "/foo/bar", "list": ["red", "green", "blue"], "keys": {"semi": ";", "dot": ".", "comma": ","}, "v": "6", "x": "1024", "y": "768", "empty": "", "empty_keys": [], "undef": null}, "testcases": [["{#var}", "#value"], ["{#hello}", "#Hello%20World!"], ["{#half}", "#50%25"], ["foo{#empty}", "foo#"], ["foo{#undef}", "foo"], ["{#x,hello,y}", "#1024,Hello%20World!,768"], ["{#path,x}/here", "#/foo/bar,1024/here"], ["{#path:6}/here", "#/foo/b/here"], ["{#list}", "#red,green,blue"], ["{#list*}", "#red,green,blue"], ["{#keys}", ["#comma,,,dot,.,semi,;", "#comma,,,semi,;,dot,.", "#dot,.,comma,,,semi,;", "#dot,.,semi,;,comma,,", "#semi,;,comma,,,dot,.", "#semi,;,dot,.,comma,,"]]]}, "3.2.5 Label Expansion with Dot-Prefix": {"variables": {"count": ["one", "two", "three"], "dom": ["example", "com"], "dub": "me/too", "hello": "Hello World!", "half": "50%", "var": "value", "who": "fred", "base": "http://example.com/home/", "path": "/foo/bar", "list": ["red", "green", "blue"], "keys": {"semi": ";", "dot": ".", "comma": ","}, "v": "6", "x": "1024", "y": "768", "empty": "", "empty_keys": [], "undef": null}, "testcases": [["{.who}", ".fred"], ["{.who,who}", ".fred.fred"], ["{.half,who}", ".50%25.fred"], ["www{.dom*}", "www.example.com"], ["X{.var}", "X.value"], ["X{.var:3}", "<PERSON>.val"], ["X{.empty}", "X."], ["X{.undef}", "X"], ["X{.list}", "X.red,green,blue"], ["X{.list*}", "X.red.green.blue"], ["{#keys}", ["#comma,,,dot,.,semi,;", "#comma,,,semi,;,dot,.", "#dot,.,comma,,,semi,;", "#dot,.,semi,;,comma,,", "#semi,;,comma,,,dot,.", "#semi,;,dot,.,comma,,"]], ["{#keys*}", ["#comma=,,dot=.,semi=;", "#comma=,,semi=;,dot=.", "#dot=.,comma=,,semi=;", "#dot=.,semi=;,comma=,", "#semi=;,comma=,,dot=.", "#semi=;,dot=.,comma=,"]], ["X{.empty_keys}", "X"], ["X{.empty_keys*}", "X"]]}, "3.2.6 Path Segment Expansion": {"variables": {"count": ["one", "two", "three"], "dom": ["example", "com"], "dub": "me/too", "hello": "Hello World!", "half": "50%", "var": "value", "who": "fred", "base": "http://example.com/home/", "path": "/foo/bar", "list": ["red", "green", "blue"], "keys": {"semi": ";", "dot": ".", "comma": ","}, "v": "6", "x": "1024", "y": "768", "empty": "", "empty_keys": [], "undef": null}, "testcases": [["{/who}", "/fred"], ["{/who,who}", "/fred/fred"], ["{/half,who}", "/50%25/fred"], ["{/who,dub}", "/fred/me%2Ftoo"], ["{/var}", "/value"], ["{/var,empty}", "/value/"], ["{/var,undef}", "/value"], ["{/var,x}/here", "/value/1024/here"], ["{/var:1,var}", "/v/value"], ["{/list}", "/red,green,blue"], ["{/list*}", "/red/green/blue"], ["{/list*,path:4}", "/red/green/blue/%2Ffoo"], ["{/keys}", ["/comma,%2C,dot,.,semi,%3B", "/comma,%2C,semi,%3B,dot,.", "/dot,.,comma,%2C,semi,%3B", "/dot,.,semi,%3B,comma,%2C", "/semi,%3B,comma,%2C,dot,.", "/semi,%3B,dot,.,comma,%2C"]], ["{/keys*}", ["/comma=%2C/dot=./semi=%3B", "/comma=%2C/semi=%3B/dot=.", "/dot=./comma=%2C/semi=%3B", "/dot=./semi=%3B/comma=%2C", "/semi=%3B/comma=%2C/dot=.", "/semi=%3B/dot=./comma=%2C"]]]}, "3.2.7 Path-Style Parameter Expansion": {"variables": {"count": ["one", "two", "three"], "dom": ["example", "com"], "dub": "me/too", "hello": "Hello World!", "half": "50%", "var": "value", "who": "fred", "base": "http://example.com/home/", "path": "/foo/bar", "list": ["red", "green", "blue"], "keys": {"semi": ";", "dot": ".", "comma": ","}, "v": "6", "x": "1024", "y": "768", "empty": "", "empty_keys": [], "undef": null}, "testcases": [["{;who}", ";who=fred"], ["{;half}", ";half=50%25"], ["{;empty}", ";empty"], ["{;hello:5}", ";hello=Hello"], ["{;v,empty,who}", ";v=6;empty;who=fred"], ["{;v,bar,who}", ";v=6;who=fred"], ["{;x,y}", ";x=1024;y=768"], ["{;x,y,empty}", ";x=1024;y=768;empty"], ["{;x,y,undef}", ";x=1024;y=768"], ["{;list}", ";list=red,green,blue"], ["{;list*}", ";list=red;list=green;list=blue"], ["{;keys}", [";keys=comma,%2C,dot,.,semi,%3B", ";keys=comma,%2C,semi,%3B,dot,.", ";keys=dot,.,comma,%2C,semi,%3B", ";keys=dot,.,semi,%3B,comma,%2C", ";keys=semi,%3B,comma,%2C,dot,.", ";keys=semi,%3B,dot,.,comma,%2C"]], ["{;keys*}", [";comma=%2C;dot=.;semi=%3B", ";comma=%2C;semi=%3B;dot=.", ";dot=.;comma=%2C;semi=%3B", ";dot=.;semi=%3B;comma=%2C", ";semi=%3B;comma=%2C;dot=.", ";semi=%3B;dot=.;comma=%2C"]]]}, "3.2.8 Form-Style Query Expansion": {"variables": {"count": ["one", "two", "three"], "dom": ["example", "com"], "dub": "me/too", "hello": "Hello World!", "half": "50%", "var": "value", "who": "fred", "base": "http://example.com/home/", "path": "/foo/bar", "list": ["red", "green", "blue"], "keys": {"semi": ";", "dot": ".", "comma": ","}, "v": "6", "x": "1024", "y": "768", "empty": "", "empty_keys": [], "undef": null}, "testcases": [["{?who}", "?who=fred"], ["{?half}", "?half=50%25"], ["{?x,y}", "?x=1024&y=768"], ["{?x,y,empty}", "?x=1024&y=768&empty="], ["{?x,y,undef}", "?x=1024&y=768"], ["{?var:3}", "?var=val"], ["{?list}", "?list=red,green,blue"], ["{?list*}", "?list=red&list=green&list=blue"], ["{?keys}", ["?keys=comma,%2C,dot,.,semi,%3B", "?keys=comma,%2C,semi,%3B,dot,.", "?keys=dot,.,comma,%2C,semi,%3B", "?keys=dot,.,semi,%3B,comma,%2C", "?keys=semi,%3B,comma,%2C,dot,.", "?keys=semi,%3B,dot,.,comma,%2C"]], ["{?keys*}", ["?comma=%2C&dot=.&semi=%3B", "?comma=%2C&semi=%3B&dot=.", "?dot=.&comma=%2C&semi=%3B", "?dot=.&semi=%3B&comma=%2C", "?semi=%3B&comma=%2C&dot=.", "?semi=%3B&dot=.&comma=%2C"]]]}, "3.2.9 Form-Style Query Continuation": {"variables": {"count": ["one", "two", "three"], "dom": ["example", "com"], "dub": "me/too", "hello": "Hello World!", "half": "50%", "var": "value", "who": "fred", "base": "http://example.com/home/", "path": "/foo/bar", "list": ["red", "green", "blue"], "keys": {"semi": ";", "dot": ".", "comma": ","}, "v": "6", "x": "1024", "y": "768", "empty": "", "empty_keys": [], "undef": null}, "testcases": [["{&who}", "&who=fred"], ["{&half}", "&half=50%25"], ["?fixed=yes{&x}", "?fixed=yes&x=1024"], ["{&var:3}", "&var=val"], ["{&x,y,empty}", "&x=1024&y=768&empty="], ["{&x,y,undef}", "&x=1024&y=768"], ["{&list}", "&list=red,green,blue"], ["{&list*}", "&list=red&list=green&list=blue"], ["{&keys}", ["&keys=comma,%2C,dot,.,semi,%3B", "&keys=comma,%2C,semi,%3B,dot,.", "&keys=dot,.,comma,%2C,semi,%3B", "&keys=dot,.,semi,%3B,comma,%2C", "&keys=semi,%3B,comma,%2C,dot,.", "&keys=semi,%3B,dot,.,comma,%2C"]], ["{&keys*}", ["&comma=%2C&dot=.&semi=%3B", "&comma=%2C&semi=%3B&dot=.", "&dot=.&comma=%2C&semi=%3B", "&dot=.&semi=%3B&comma=%2C", "&semi=%3B&comma=%2C&dot=.", "&semi=%3B&dot=.&comma=%2C"]]]}}