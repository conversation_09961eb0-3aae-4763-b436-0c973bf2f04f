"use strict";
// Copyright 2023 Google LLC
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.USER_AGENT = exports.PRODUCT_NAME = exports.pkg = void 0;
const pkg = require('../../package.json');
exports.pkg = pkg;
const PRODUCT_NAME = 'google-api-nodejs-client';
exports.PRODUCT_NAME = PRODUCT_NAME;
const USER_AGENT = `${PRODUCT_NAME}/${pkg.version}`;
exports.USER_AGENT = USER_AGENT;
//# sourceMappingURL=shared.cjs.map