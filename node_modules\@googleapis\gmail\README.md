<img src="https://avatars0.githubusercontent.com/u/1342004?v=3&s=96" alt="Google Inc. logo" title="Google" align="right" height="96" width="96"/>

# gmail

> The Gmail API lets you view and manage Gmail mailbox data like threads, messages, and labels.

## Installation

```sh
$ npm install @googleapis/gmail
```

## Usage
All documentation and usage information can be found on [GitHub](https://github.com/googleapis/google-api-nodejs-client).
Information on classes can be found in [Googleapis Documentation](https://googleapis.dev/nodejs/googleapis/latest/gmail/classes/Gmail.html).

## License
This library is licensed under Apache 2.0. Full license text is available in [LICENSE](https://github.com/googleapis/google-api-nodejs-client/blob/main/LICENSE).

## Contributing
We love contributions! Before submitting a Pull Request, it's always good to start with a new issue first. To learn more, see [CONTRIBUTING](https://github.com/google/google-api-nodejs-client/blob/main/.github/CONTRIBUTING.md).

## Questions/problems?
* Ask your development related questions on [StackOverflow](http://stackoverflow.com/questions/tagged/google-api-nodejs-client).
* If you've found an bug/issue, please [file it on GitHub](https://github.com/googleapis/google-api-nodejs-client/issues).


*Crafted with ❤️ by the Google Node.js team*
