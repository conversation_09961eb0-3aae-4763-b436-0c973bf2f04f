{"name": "gaxios", "version": "7.1.0", "description": "A simple common HTTP client specifically for Google APIs and services.", "main": "build/cjs/src/index.js", "types": "build/cjs/src/index.d.ts", "files": ["build/"], "exports": {".": {"import": {"types": "./build/esm/src/index.d.ts", "default": "./build/esm/src/index.js"}, "require": {"types": "./build/cjs/src/index.d.ts", "default": "./build/cjs/src/index.js"}}}, "scripts": {"lint": "gts check --no-inline-config", "test": "c8 mocha build/esm/test", "presystem-test": "npm run compile", "system-test": "mocha build/esm/system-test --timeout 80000", "compile": "tsc -b ./tsconfig.json ./tsconfig.cjs.json && node utils/enable-esm.mjs", "fix": "gts fix", "prepare": "npm run compile", "pretest": "npm run compile", "webpack": "webpack", "prebrowser-test": "npm run compile", "browser-test": "node build/browser-test/browser-test-runner.js", "docs": "jsdoc -c .jsdoc.js", "docs-test": "linkinator docs", "predocs-test": "npm run docs", "samples-test": "cd samples/ && npm link ../ && npm test && cd ../", "prelint": "cd samples; npm link ../; npm install", "clean": "gts clean"}, "repository": "googleapis/gaxios", "keywords": ["google"], "engines": {"node": ">=18"}, "author": "Google, LLC", "license": "Apache-2.0", "devDependencies": {"@babel/plugin-proposal-private-methods": "^7.18.6", "@types/cors": "^2.8.6", "@types/express": "^5.0.0", "@types/extend": "^3.0.1", "@types/mocha": "^10.0.10", "@types/multiparty": "4.2.1", "@types/mv": "^2.1.0", "@types/ncp": "^2.0.1", "@types/node": "^22.0.0", "@types/sinon": "^17.0.0", "@types/tmp": "0.2.6", "assert": "^2.0.0", "browserify": "^17.0.0", "c8": "^10.0.0", "cors": "^2.8.5", "express": "^5.0.0", "gts": "^6.0.0", "is-docker": "^3.0.0", "jsdoc": "^4.0.0", "jsdoc-fresh": "^4.0.0", "jsdoc-region-tag": "^3.0.0", "karma": "^6.0.0", "karma-chrome-launcher": "^3.0.0", "karma-coverage": "^2.0.0", "karma-firefox-launcher": "^2.0.0", "karma-mocha": "^2.0.0", "karma-remap-coverage": "^0.1.5", "karma-sourcemap-loader": "^0.4.0", "karma-webpack": "^5.0.1", "linkinator": "^6.1.2", "mocha": "^11.1.0", "multiparty": "^4.2.1", "mv": "^2.1.1", "ncp": "^2.0.0", "nock": "^14.0.0-beta.13", "null-loader": "^4.0.0", "pack-n-play": "^3.0.0", "puppeteer": "^24.0.0", "sinon": "^20.0.0", "stream-browserify": "^3.0.0", "tmp": "0.2.3", "ts-loader": "^9.5.2", "typescript": "^5.1.6", "webpack": "^5.35.0", "webpack-cli": "^6.0.1"}, "dependencies": {"extend": "^3.0.2", "https-proxy-agent": "^7.0.1", "node-fetch": "^3.3.2"}}