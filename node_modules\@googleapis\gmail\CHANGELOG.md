# Changelog

## [13.0.1](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v13.0.0...gmail-v13.0.1) (2025-06-04)


### Bug Fixes

* **deps:** upgrade googleapis-common to 8.0.2-rc ([f4b0990](https://github.com/googleapis/google-api-nodejs-client/commit/f4b099071040cfbcfe4a2e7d487d45ee93b369e0))

## [13.0.0](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v12.0.1...gmail-v13.0.0) (2025-05-29)


### ⚠ BREAKING CHANGES

* upgrade to node 18

### Features

* upgrade to node 18 ([682fbb8](https://github.com/googleapis/google-api-nodejs-client/commit/682fbb869189ae92b3e9a194d37d0548af0c1f92))

## [12.0.1](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v12.0.0...gmail-v12.0.1) (2025-05-05)


### Bug Fixes

* **gmail:** update the API ([de3b427](https://github.com/googleapis/google-api-nodejs-client/commit/de3b4270e7fb2602a68c250df6779063469aec2e))

## [12.0.0](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v11.0.0...gmail-v12.0.0) (2024-06-21)


### ⚠ BREAKING CHANGES

* **gmail:** This release has breaking changes.

### Features

* **gmail:** update the API ([68cef12](https://github.com/googleapis/google-api-nodejs-client/commit/68cef1291d7bb0e6a088f6b95a8998144e79d406))
* **gmail:** update the API ([a4d9319](https://github.com/googleapis/google-api-nodejs-client/commit/a4d9319ad50bbfd9e27ed7b4ff865951b7dd1032))

## [11.0.0](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v10.0.0...gmail-v11.0.0) (2024-06-06)


### ⚠ BREAKING CHANGES

* **gmail:** This release has breaking changes.

### Features

* **gmail:** update the API ([68cef12](https://github.com/googleapis/google-api-nodejs-client/commit/68cef1291d7bb0e6a088f6b95a8998144e79d406))
* **gmail:** update the API ([a4d9319](https://github.com/googleapis/google-api-nodejs-client/commit/a4d9319ad50bbfd9e27ed7b4ff865951b7dd1032))

## [10.0.0](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v9.2.0...gmail-v10.0.0) (2024-06-03)


### ⚠ BREAKING CHANGES

* **gmail:** This release has breaking changes.

### Features

* **gmail:** update the API ([68cef12](https://github.com/googleapis/google-api-nodejs-client/commit/68cef1291d7bb0e6a088f6b95a8998144e79d406))

## [9.2.0](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v9.1.0...gmail-v9.2.0) (2024-05-02)


### Features

* **gmail:** update the API ([a4d9319](https://github.com/googleapis/google-api-nodejs-client/commit/a4d9319ad50bbfd9e27ed7b4ff865951b7dd1032))

## [9.1.0](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v9.0.0...gmail-v9.1.0) (2024-05-02)


### Features

* **gmail:** update the API ([a4d9319](https://github.com/googleapis/google-api-nodejs-client/commit/a4d9319ad50bbfd9e27ed7b4ff865951b7dd1032))

## [9.0.0](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v8.0.0...gmail-v9.0.0) (2024-01-05)


### ⚠ BREAKING CHANGES

* **gmail:** This release has breaking changes.
* This release has breaking changes.

### Features

* **gmail:** update the API ([c7698bd](https://github.com/googleapis/google-api-nodejs-client/commit/c7698bda1d9a8a034d92a63c27d9af74a416d94b))
* **gmail:** update the API ([1fe47cf](https://github.com/googleapis/google-api-nodejs-client/commit/1fe47cf81e9656a15f085ad859b0968f0ae24569))
* **gmail:** update the API ([e943c90](https://github.com/googleapis/google-api-nodejs-client/commit/e943c90dbba2603cafb29e8045ba8969f1d51505))
* run the generator ([#3355](https://github.com/googleapis/google-api-nodejs-client/issues/3355)) ([5504c86](https://github.com/googleapis/google-api-nodejs-client/commit/5504c86fd61740886047320e2ed70f02a164acd7))

## [8.0.0](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v7.0.0...gmail-v8.0.0) (2024-01-03)


### ⚠ BREAKING CHANGES

* **gmail:** This release has breaking changes.

### Features

* **gmail:** update the API ([c7698bd](https://github.com/googleapis/google-api-nodejs-client/commit/c7698bda1d9a8a034d92a63c27d9af74a416d94b))

## [7.0.0](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v6.1.0...gmail-v7.0.0) (2023-11-30)


### ⚠ BREAKING CHANGES

* This release has breaking changes.

### Features

* **gmail:** update the API ([1fe47cf](https://github.com/googleapis/google-api-nodejs-client/commit/1fe47cf81e9656a15f085ad859b0968f0ae24569))
* **gmail:** update the API ([e943c90](https://github.com/googleapis/google-api-nodejs-client/commit/e943c90dbba2603cafb29e8045ba8969f1d51505))
* run the generator ([#3355](https://github.com/googleapis/google-api-nodejs-client/issues/3355)) ([5504c86](https://github.com/googleapis/google-api-nodejs-client/commit/5504c86fd61740886047320e2ed70f02a164acd7))

## [6.1.0](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v6.0.0...gmail-v6.1.0) (2023-11-27)


### Features

* **gmail:** update the API ([1fe47cf](https://github.com/googleapis/google-api-nodejs-client/commit/1fe47cf81e9656a15f085ad859b0968f0ae24569))

## [6.0.0](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v5.0.4...gmail-v6.0.0) (2023-10-11)


### ⚠ BREAKING CHANGES

* This release has breaking changes.

### Features

* **gmail:** update the API ([e943c90](https://github.com/googleapis/google-api-nodejs-client/commit/e943c90dbba2603cafb29e8045ba8969f1d51505))
* run the generator ([#3355](https://github.com/googleapis/google-api-nodejs-client/issues/3355)) ([5504c86](https://github.com/googleapis/google-api-nodejs-client/commit/5504c86fd61740886047320e2ed70f02a164acd7))


### Bug Fixes

* **deps:** update dependency googleapis-common to v7 ([9491ec1](https://github.com/googleapis/google-api-nodejs-client/commit/9491ec1cdc3c413e7d73edcfcd59cf5c28a7c855))
* **gmail:** update the API ([6ee8730](https://github.com/googleapis/google-api-nodejs-client/commit/6ee873005b2b97a54a4a7d3af9d8ae0d42c6ca6e))

## [5.0.4](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v5.0.3...gmail-v5.0.4) (2023-08-25)


### Bug Fixes

* **deps:** update dependency googleapis-common to v7 ([9491ec1](https://github.com/googleapis/google-api-nodejs-client/commit/9491ec1cdc3c413e7d73edcfcd59cf5c28a7c855))

## [5.0.3](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v5.0.2...gmail-v5.0.3) (2023-08-17)


### Bug Fixes

* **gmail:** update the API ([6ee8730](https://github.com/googleapis/google-api-nodejs-client/commit/6ee873005b2b97a54a4a7d3af9d8ae0d42c6ca6e))

## [5.0.2](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v5.0.1...gmail-v5.0.2) (2023-08-17)


### Bug Fixes

* **gmail:** update the API ([6ee8730](https://github.com/googleapis/google-api-nodejs-client/commit/6ee873005b2b97a54a4a7d3af9d8ae0d42c6ca6e))

## [5.0.1](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v5.0.0...gmail-v5.0.1) (2023-08-09)


### Bug Fixes

* **gmail:** update the API ([6ee8730](https://github.com/googleapis/google-api-nodejs-client/commit/6ee873005b2b97a54a4a7d3af9d8ae0d42c6ca6e))

## [5.0.0](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v4.0.0...gmail-v5.0.0) (2023-07-10)


### ⚠ BREAKING CHANGES

* This release has breaking changes.
* This release has breaking changes.

### Features

* **gmail:** update the API ([45acd52](https://github.com/googleapis/google-api-nodejs-client/commit/45acd522ec16e9c91190060190c539e6e8867a85))
* regenerate index files ([bb9d246](https://github.com/googleapis/google-api-nodejs-client/commit/bb9d246d1e0c7c22e2ff2b4c40ed5df4fa410e12))
* run the generator ([#3208](https://github.com/googleapis/google-api-nodejs-client/issues/3208)) ([f040752](https://github.com/googleapis/google-api-nodejs-client/commit/f0407528c81f2e74ba5e1d35443085d35f5f005d))
* run the generator ([#3260](https://github.com/googleapis/google-api-nodejs-client/issues/3260)) ([8efb786](https://github.com/googleapis/google-api-nodejs-client/commit/8efb7861b7da4bc1472a4b654e46f90b29fbff20))


### Bug Fixes

* **gmail:** update the API ([6ff8ff8](https://github.com/googleapis/google-api-nodejs-client/commit/6ff8ff8eeaac6b0245abf3e1543e891ac96eff86))
* **gmail:** update the API ([c6fa1dc](https://github.com/googleapis/google-api-nodejs-client/commit/c6fa1dc47d81d49492f7e81a13b816326840340d))

## [4.0.0](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v3.0.0...gmail-v4.0.0) (2023-07-10)


### ⚠ BREAKING CHANGES

* This release has breaking changes.
* This release has breaking changes.

### Features

* **gmail:** update the API ([45acd52](https://github.com/googleapis/google-api-nodejs-client/commit/45acd522ec16e9c91190060190c539e6e8867a85))
* regenerate index files ([bb9d246](https://github.com/googleapis/google-api-nodejs-client/commit/bb9d246d1e0c7c22e2ff2b4c40ed5df4fa410e12))
* run the generator ([#3208](https://github.com/googleapis/google-api-nodejs-client/issues/3208)) ([f040752](https://github.com/googleapis/google-api-nodejs-client/commit/f0407528c81f2e74ba5e1d35443085d35f5f005d))
* run the generator ([#3260](https://github.com/googleapis/google-api-nodejs-client/issues/3260)) ([8efb786](https://github.com/googleapis/google-api-nodejs-client/commit/8efb7861b7da4bc1472a4b654e46f90b29fbff20))


### Bug Fixes

* **gmail:** update the API ([6ff8ff8](https://github.com/googleapis/google-api-nodejs-client/commit/6ff8ff8eeaac6b0245abf3e1543e891ac96eff86))
* **gmail:** update the API ([c6fa1dc](https://github.com/googleapis/google-api-nodejs-client/commit/c6fa1dc47d81d49492f7e81a13b816326840340d))

## [3.0.0](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v2.0.1...gmail-v3.0.0) (2023-06-22)


### ⚠ BREAKING CHANGES

* This release has breaking changes.

### Features

* run the generator ([#3260](https://github.com/googleapis/google-api-nodejs-client/issues/3260)) ([8efb786](https://github.com/googleapis/google-api-nodejs-client/commit/8efb7861b7da4bc1472a4b654e46f90b29fbff20))

## [2.0.1](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v2.0.0...gmail-v2.0.1) (2023-05-02)


### Bug Fixes

* **gmail:** update the API ([6ff8ff8](https://github.com/googleapis/google-api-nodejs-client/commit/6ff8ff8eeaac6b0245abf3e1543e891ac96eff86))

## [2.0.0](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v1.2.0...gmail-v2.0.0) (2023-04-08)


### ⚠ BREAKING CHANGES

* This release has breaking changes.

### Features

* run the generator ([#3208](https://github.com/googleapis/google-api-nodejs-client/issues/3208)) ([f040752](https://github.com/googleapis/google-api-nodejs-client/commit/f0407528c81f2e74ba5e1d35443085d35f5f005d))

## [1.2.0](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v1.1.1...gmail-v1.2.0) (2023-02-24)


### Features

* **gmail:** update the API ([d57935a](https://github.com/googleapis/google-api-nodejs-client/commit/d57935a2b8a0056dd8cfe8ceddfb90dbe4ac01cb))
* regenerate index files ([bb9d246](https://github.com/googleapis/google-api-nodejs-client/commit/bb9d246d1e0c7c22e2ff2b4c40ed5df4fa410e12))


### Bug Fixes

* **gmail:** update the API ([c6fa1dc](https://github.com/googleapis/google-api-nodejs-client/commit/c6fa1dc47d81d49492f7e81a13b816326840340d))
* **gmail:** update the API ([454caaf](https://github.com/googleapis/google-api-nodejs-client/commit/454caaf1e282d8afb2dd933ec6cf56c1193a27e5))

## [1.1.1](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v1.1.0...gmail-v1.1.1) (2023-02-10)


### Bug Fixes

* **gmail:** update the API ([454caaf](https://github.com/googleapis/google-api-nodejs-client/commit/454caaf1e282d8afb2dd933ec6cf56c1193a27e5))

## [1.1.0](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v1.0.0...gmail-v1.1.0) (2022-12-22)


### Features

* **gmail:** update the API ([d57935a](https://github.com/googleapis/google-api-nodejs-client/commit/d57935a2b8a0056dd8cfe8ceddfb90dbe4ac01cb))

## [1.0.0](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v0.3.5...gmail-v1.0.0) (2022-10-28)


### ⚠ BREAKING CHANGES

* **build:** This release has breaking changes.

### Features

* **build:** run the generator (submodules now require Node 12) ([#3073](https://github.com/googleapis/google-api-nodejs-client/issues/3073)) ([eda0707](https://github.com/googleapis/google-api-nodejs-client/commit/eda07079dadab46a80b6f9ede618f4f43030169e))

## [0.3.5](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v0.3.4...gmail-v0.3.5) (2022-09-21)


### Bug Fixes

* **gmail:** update the API ([704ac32](https://github.com/googleapis/google-api-nodejs-client/commit/704ac329ebb44c1b5ad749a7140102604f064d51))

## [0.3.4](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v0.3.3...gmail-v0.3.4) (2022-06-21)


### Bug Fixes

* **gmail:** update the API ([92cc465](https://github.com/googleapis/google-api-nodejs-client/commit/92cc46533a1c555da161f856c4e82709ebced078))

### [0.3.3](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v0.3.2...gmail-v0.3.3) (2022-04-11)


### Bug Fixes

* **gmail:** update the API ([f0098dd](https://github.com/googleapis/google-api-nodejs-client/commit/f0098dda2a3aebec3382bef38cdc70c129aeb12f))

### [0.3.2](https://github.com/googleapis/google-api-nodejs-client/compare/gmail-v0.3.1...gmail-v0.3.2) (2022-03-14)


### Bug Fixes

* **gmail:** update the API ([ddb0908](https://github.com/googleapis/google-api-nodejs-client/commit/ddb0908f9b9c18f1c6ad7d9f94bfabb47cad381a))

### [0.3.1](https://www.github.com/googleapis/google-api-nodejs-client/compare/gmail-v0.3.0...gmail-v0.3.1) (2021-11-25)


### Bug Fixes

* **gmail:** update the API ([607faf3](https://www.github.com/googleapis/google-api-nodejs-client/commit/607faf32c5f9755f53fc05729bcb3388b351342d))

## [0.3.0](https://www.github.com/googleapis/google-api-nodejs-client/compare/gmail-v0.2.3...gmail-v0.3.0) (2021-09-14)


### Features

* **auth:** adds support workload identity federation ([#2517](https://www.github.com/googleapis/google-api-nodejs-client/issues/2517)) ([a10707c](https://www.github.com/googleapis/google-api-nodejs-client/commit/a10707c477759e7c9ef6360a2fe800856fb600c1))


### Bug Fixes

* **deps:** update dependency googleapis-common to v5 ([#2497](https://www.github.com/googleapis/google-api-nodejs-client/issues/2497)) ([fec087a](https://www.github.com/googleapis/google-api-nodejs-client/commit/fec087abcf3d994dd41c3ffa0a0c12b1f9f09dae))
* **gmail:** update the API ([e4ac07b](https://www.github.com/googleapis/google-api-nodejs-client/commit/e4ac07b60a056c90afb417012f7ccc892bb7e657))
* **gmail:** update the API ([e85f5fb](https://www.github.com/googleapis/google-api-nodejs-client/commit/e85f5fb6c39a074ad552acc9e3aa18313f013373))
* **gmail:** update the API ([86f29db](https://www.github.com/googleapis/google-api-nodejs-client/commit/86f29db6a50474fdc9665f13491cbc705c6a4a51))
* **gmail:** update the API ([dfaefe0](https://www.github.com/googleapis/google-api-nodejs-client/commit/dfaefe07aea2194a9c713d65ecd42872e0e49655))
* **gmail:** update the API ([a83b049](https://www.github.com/googleapis/google-api-nodejs-client/commit/a83b049526e0f492910a756b2dfecf3585e36de8))
* upgrade webpack from 4 to 5  ([#2524](https://www.github.com/googleapis/google-api-nodejs-client/issues/2524)) ([ecfd8bf](https://www.github.com/googleapis/google-api-nodejs-client/commit/ecfd8bfcd06e1beabff7ec9a8c4000222379eb8d))

### [0.2.3](https://www.github.com/googleapis/google-api-nodejs-client/compare/gmail-v0.2.2...gmail-v0.2.3) (2021-06-18)


### Bug Fixes

* **gmail:** update the API ([e85f5fb](https://www.github.com/googleapis/google-api-nodejs-client/commit/e85f5fb6c39a074ad552acc9e3aa18313f013373))

### [0.2.2](https://www.github.com/googleapis/google-api-nodejs-client/compare/gmail-v0.2.1...gmail-v0.2.2) (2021-06-09)


### Bug Fixes

* **gmail:** update the API ([86f29db](https://www.github.com/googleapis/google-api-nodejs-client/commit/86f29db6a50474fdc9665f13491cbc705c6a4a51))

### [0.2.1](https://www.github.com/googleapis/google-api-nodejs-client/compare/gmail-v0.2.0...gmail-v0.2.1) (2021-05-23)


### Bug Fixes

* **gmail:** update the API ([dfaefe0](https://www.github.com/googleapis/google-api-nodejs-client/commit/dfaefe07aea2194a9c713d65ecd42872e0e49655))

## [0.2.0](https://www.github.com/googleapis/google-api-nodejs-client/compare/gmail-v0.1.0...gmail-v0.2.0) (2021-03-18)


### Features

* **auth:** adds support workload identity federation ([#2517](https://www.github.com/googleapis/google-api-nodejs-client/issues/2517)) ([a10707c](https://www.github.com/googleapis/google-api-nodejs-client/commit/a10707c477759e7c9ef6360a2fe800856fb600c1))


### Bug Fixes

* **deps:** update dependency googleapis-common to v5 ([#2497](https://www.github.com/googleapis/google-api-nodejs-client/issues/2497)) ([fec087a](https://www.github.com/googleapis/google-api-nodejs-client/commit/fec087abcf3d994dd41c3ffa0a0c12b1f9f09dae))
* **gmailpostmastertools:** update the API ([0ed0d0c](https://www.github.com/googleapis/google-api-nodejs-client/commit/0ed0d0c586c998c118216451f1ef92eb57c97693))
* **gmail:** update the API ([a83b049](https://www.github.com/googleapis/google-api-nodejs-client/commit/a83b049526e0f492910a756b2dfecf3585e36de8))
* upgrade webpack from 4 to 5  ([#2524](https://www.github.com/googleapis/google-api-nodejs-client/issues/2524)) ([ecfd8bf](https://www.github.com/googleapis/google-api-nodejs-client/commit/ecfd8bfcd06e1beabff7ec9a8c4000222379eb8d))
