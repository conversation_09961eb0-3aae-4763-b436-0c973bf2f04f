* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', '<PERSON><PERSON>s', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #1a237e 0%, #3f51b5 100%);
  min-height: 100vh;
  line-height: 1.6;
  color: #333;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Global Styles */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-container {
  min-height: calc(100vh - 80px);
  padding: 20px;
  background: rgba(255, 255, 255, 0.95);
  margin: 20px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin: 20px 0;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.course-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8f9ff 100%);
  border-radius: 20px;
  padding: 24px;
  margin: 16px 0;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(102, 126, 234, 0.1);
  position: relative;
  overflow: hidden;
}

.course-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1a237e, #3f51b5);
}

.course-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 40px rgba(102, 126, 234, 0.2);
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  margin: 8px;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-width: 120px;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.btn:active {
  transform: translateY(-1px);
}

.btn-primary {
  background: linear-gradient(135deg, #1a237e 0%, #3f51b5 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(26, 35, 126, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

.btn-success:hover {
  background: linear-gradient(135deg, #3d8bfe 0%, #00d4fe 100%);
  box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
}

.btn-danger {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.btn-danger:hover {
  background: linear-gradient(135deg, #ff5252 0%, #d32f2f 100%);
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, #a8a8a8 0%, #8d8d8d 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(168, 168, 168, 0.3);
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #9e9e9e 0%, #757575 100%);
  box-shadow: 0 8px 25px rgba(168, 168, 168, 0.4);
}

.btn-outline {
  background: transparent;
  border: 2px solid #1a237e;
  color: #1a237e;
  box-shadow: none;
}

.btn-outline:hover {
  background: #1a237e;
  color: white;
  box-shadow: 0 8px 25px rgba(26, 35, 126, 0.3);
}

.form-group {
  margin-bottom: 24px;
  position: relative;
}

.form-control {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  border-radius: 12px;
  font-size: 16px;
  background: rgba(255, 255, 255, 0.9);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.form-control:focus {
  outline: none;
  border-color: #1a237e;
  box-shadow: 0 0 0 4px rgba(26, 35, 126, 0.1);
  background: white;
  transform: translateY(-2px);
}

.form-control::placeholder {
  color: rgba(51, 51, 51, 0.6);
  transition: opacity 0.3s ease;
}

.form-control:focus::placeholder {
  opacity: 0.8;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-floating {
  position: relative;
}

.form-floating .form-control {
  padding-top: 24px;
  padding-bottom: 8px;
}

.form-floating .form-label {
  position: absolute;
  top: 16px;
  left: 20px;
  transition: all 0.3s ease;
  pointer-events: none;
  color: rgba(51, 51, 51, 0.6);
  text-transform: none;
  letter-spacing: normal;
}

.form-floating .form-control:focus ~ .form-label,
.form-floating .form-control:not(:placeholder-shown) ~ .form-label {
  top: 8px;
  font-size: 12px;
  color: #1a237e;
  font-weight: 600;
}

.text-center {
  text-align: center;
}

.mt-3 {
  margin-top: 1rem;
}

.mb-3 {
  margin-bottom: 1rem;
}

.p-3 {
  padding: 1rem;
}

/* Navigation Styles */
.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

/* Course Grid */
.course-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
  padding: 20px 0;
}

/* Video Player Styles */
.video-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  background: #000;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
}

.video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Progress Bar */
.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(102, 126, 234, 0.2);
  border-radius: 4px;
  overflow: hidden;
  margin: 16px 0;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1a237e, #3f51b5);
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* Quiz Styles */
.quiz-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 32px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.quiz-question {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #333;
  line-height: 1.4;
}

.quiz-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.quiz-option {
  padding: 16px 20px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
}

.quiz-option:hover {
  border-color: #1a237e;
  background: rgba(26, 35, 126, 0.05);
  transform: translateX(4px);
}

.quiz-option.selected {
  border-color: #1a237e;
  background: rgba(26, 35, 126, 0.1);
  color: #1a237e;
  font-weight: 600;
}

.quiz-option.correct {
  border-color: #4facfe;
  background: rgba(79, 172, 254, 0.1);
  color: #4facfe;
}

.quiz-option.incorrect {
  border-color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
  color: #ff6b6b;
}

/* Dashboard Styles */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  background: linear-gradient(135deg, #1a237e 0%, #3f51b5 100%);
  color: white;
  padding: 24px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 8px 25px rgba(26, 35, 126, 0.3);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.pulse {
  animation: pulse 2s infinite;
}

/* Loading Spinner */
.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(26, 35, 126, 0.2);
  border-left: 4px solid #1a237e;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 20px auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .page-container {
    margin: 10px;
    padding: 16px;
    border-radius: 16px;
  }

  .card, .course-card {
    margin: 12px 0;
    padding: 20px;
  }

  .btn {
    width: 100%;
    margin: 8px 0;
  }

  .course-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .dashboard-stats {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .quiz-container {
    padding: 20px;
    margin: 10px;
  }

  .stat-number {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .form-control {
    padding: 14px 16px;
  }

  .btn {
    padding: 14px 20px;
    font-size: 14px;
  }

  .quiz-question {
    font-size: 18px;
  }

  .quiz-option {
    padding: 14px 16px;
  }
}
